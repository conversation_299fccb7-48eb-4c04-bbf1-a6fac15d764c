.mechanical-design-page-container {
  margin-bottom: 543px;

  .mechanical-design-page-content {
    .mechanical-design-header-image {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        max-width: 100%;
        height: 90vh;
        object-fit: contain;
      }
    }

    .mechanical-design-content {
      padding-left: 71px;
      padding-right: 73px;

      .mechanical-design-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-bottom: 80px;
      }

      .mechanical-design-list {
        margin-bottom: 34.11px;

        .mechanical-design-list-item {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 36px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
          display: flex;
          align-items: flex-start;

          &::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #ffffff;
            font-size: 20px;
          }
        }
      }

      .mechanical-design-flowchart {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 131.61px;
        border-bottom: 1px solid #ffffff;
      }

      .tool-design-development {
        margin-top: 96.5px;
        padding-bottom: 80px;
        // border-bottom: 1px solid #FFFFFF;

        .tool-design-title {
          font-family: Poppins;
          font-weight: 400;
          font-size: 48px;
          letter-spacing: 0%;
          text-align: center;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 50px;
          line-height: 1.2;
          width: 100%;
        }

        .tool-design-container {
          display: flex;
          // gap: 60px;
          align-items: flex-start;
          // background: rgba(0, 0, 0, 0.3);
          padding: 50px;
          border-radius: 12px;

          .tool-design-content {
            flex: 1;
            max-width: 60%;

            .tool-design-description {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              text-align: justify;
              color: #ffffff;
              margin-bottom: 30px;
            }

            .tool-design-list {
              .tool-design-list-item {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;
                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #ffffff;
                display: flex;
                align-items: flex-start;
                // padding-left: 20px;
                position: relative;
                margin-bottom: 10px;

                &::before {
                  content: "•";
                  position: absolute;
                  left: 0;
                  color: #ffffff;
                  font-size: 20px;
                }
              }
            }
          }

          .tool-design-images {
            flex: 1;
            max-width: 40%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .tool-design-img {
              width: 100%;
              max-width: 400px;
              height: auto;
              object-fit: contain;
              border-radius: 8px;
            }
          }

          // Responsive design for tablets and mobile
          @media (max-width: 1024px) {
            flex-direction: column;
            gap: 40px;
            // padding: 40px;

            .tool-design-content {
              max-width: 100%;
            }

            .tool-design-images {
              max-width: 100%;
            }
          }

          @media (max-width: 768px) {
            padding: 30px 20px;
            gap: 30px;

            .tool-design-content {
              .tool-design-description {
                font-size: 18px;
                line-height: 28px;
              }

              .tool-design-list {
                .tool-design-list-item {
                  font-size: 18px;
                  line-height: 28px;
                }
              }
            }

            .tool-design-images {
              .tool-design-img {
                max-width: 300px;
              }
            }
          }
        }

        @media (max-width: 768px) {
          .tool-design-title {
            font-size: 36px;
          }
        }
      }
    }
  }

  .simulation-analysis {
    padding: 32px 70px 159px 70px;
    margin-top: 80.5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #093246;

    .simulation-analysis-title {
      font-family: Poppins;
      font-weight: 400;
      font-size: 48px;

      letter-spacing: 0%;
      text-align: center;
      background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 65px;
    }

    .simulation-analysis-description {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      color: #ffffff;
      margin-bottom: 50px;
    }

    .simulation-analysis-images {
      display: flex;
      flex-direction: column;
      align-self: center;

      .simulation-analysis-subtitle {
        bottom: 0;
        font-family: Poppins;
        font-weight: 500;
        font-size: 24px;

        letter-spacing: 0%;
        text-align: center;
        background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 70px;
      }
    }

    .simulation-analysis-description-box {
      .simulation-analysis-testing {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 42px;

        .simulation-analysis-testing-title {
          font-family: Poppins;
          font-weight: 600;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          margin-bottom: 15px;
        }
      }
    }
  }

  .how-it-works {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 112px;

    .how-it-works-title {
      font-family: Poppins;
      font-weight: 500;
      font-size: 60px;

      letter-spacing: 0%;
      background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 40px;
    }
  }
}
